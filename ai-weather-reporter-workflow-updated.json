{"nodes": [{"parameters": {"content": "## 🚀 Enhanced Setup Instructions for AI Weather Reporter\n\n### 1. WeatherAPI.com Setup\n- Sign up at https://www.weatherapi.com/signup.aspx\n- Get your free API key from dashboard\n- Add to Environment Variables: `WEATHER_API_KEY`\n- Free tier: 1M calls/month\n\n### 2. Enhanced Supabase Database Setup\n📁 **Execute SQL file: `supabase-setup.sql`**\n- Open Supabase SQL Editor\n- Copy & paste contents from supabase-setup.sql\n- Run all commands to create table + indexes\n- Configure Supabase credentials in n8n credential manager\n- Project URL: nhjodtpylsnbazuipwvc.supabase.co\n- Use native Supabase node for better integration\n\n### 3. OpenRouter AI Setup\n- Sign up at https://openrouter.ai/\n- Get API key and add to Environment Variables: `OPENROUTER_API_KEY`\n- Model: google/gemini-flash-1.5 (fast & affordable)\n\n### 4. Gmail OAuth2 Setup (Enhanced Security)\n- Enable Gmail API in Google Cloud Console\n- Create OAuth2 credentials (Client ID + Secret)\n- Add redirect URI: `http://localhost:5678/rest/oauth2-credential/callback`\n- Configure Gmail credential in n8n with OAuth2 flow\n- Required scope: `https://www.googleapis.com/auth/gmail.send`", "height": 960, "width": 650}, "id": "0fba8133-231d-4942-bfcd-e33bff883174", "name": "📋 Enhanced Setup Instructions", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-920, 960]}, {"parameters": {"content": "## 🔗 Webhook Information\n\n**Expected POST Data:**\n```json\n{\n  \"fullName\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"city\": \"New York\"\n}\n```\n\n**Testing:**\nUse this webhook URL in your frontend form or test with Postman/curl.\n\n**Enhanced Response:** JSON with detailed success/error status and improved error handling", "height": 400, "width": 380}, "id": "578354a7-a4c8-4b44-8bc4-5cdab9526b17", "name": "🔗 Webhook Info", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1000, 240]}, {"parameters": {"httpMethod": "POST", "path": "weather-report", "responseMode": "responseNode", "options": {}}, "id": "3cb4a5a8-10ce-43a4-9593-249feb31babf", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-840, 720], "webhookId": "weather-report-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "email-validation", "leftValue": "={{ $json.body.email }}", "rightValue": "@", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "a67d740a-00ed-46eb-8595-d6803fb0e973", "name": "Email Validation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-620, 720]}, {"parameters": {"url": "=http://api.weatherapi.com/v1/current.json?key=cfbc5c365041448d906142604252505&q={{ $json.body.city }}&aqi=yes", "options": {}}, "id": "aedf35e5-c303-4d53-95c2-2ba6c1446378", "name": "Fetch Weather Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-400, 720]}, {"parameters": {"jsCode": "// Get the original webhook data from the first item\nconst webhookData = $('Webhook').first().json.body;\n// Get the weather response from current input\nconst weather = $input.first().json;\n\n// Check if weather fetch was successful\nconst weather_fetch_success = weather.current ? true : false;\n\nif (weather_fetch_success) {\n  return {\n    json: {\n      name: webhookData.fullName,\n      email: webhookData.email,\n      city: webhookData.city,\n      temperature: Math.round(weather.current.temp_c),\n      condition: weather.current.condition.text,\n      aqi: weather.current.air_quality ? Math.round(weather.current.air_quality.pm2_5) : null,\n      timestamp: new Date().toISOString(),\n      email_valid: true,\n      weather_fetch_success: true\n    }\n  };\n} else {\n  return {\n    json: {\n      name: webhookData.fullName,\n      email: webhookData.email,\n      city: webhookData.city,\n      temperature: null,\n      condition: null,\n      aqi: null,\n      timestamp: new Date().toISOString(),\n      email_valid: true,\n      weather_fetch_success: false\n    }\n  };\n}"}, "id": "db60213d-738b-4886-96c4-46c9bafede46", "name": "Process Weather Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-180, 720]}, {"parameters": {"tableId": "weather_reports", "dataToSend": "autoMapInputData"}, "id": "af67f094-1f7a-40dc-917e-d6ee6de75534", "name": "Store Initial Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [40, 720], "credentials": {"supabaseApi": {"id": "Ur7gtiGXUbgMtBAi", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "email-valid-check", "leftValue": "={{ $json.email_valid }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}, {"id": "weather-success-check", "leftValue": "={{ $json.weather_fetch_success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "5794bd0d-e35f-4634-9edd-c389241be43e", "name": "Check Email Valid & Weather Success", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [260, 720]}, {"parameters": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer 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"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "google/gemini-flash-1.5"}, {"name": "messages", "value": "=[{\"role\": \"system\", \"content\": \"You are a helpful weather assistant. Provide brief, friendly commentary about weather conditions in 1-2 sentences. Be encouraging and mention any relevant tips.\"}, {\"role\": \"user\", \"content\": \"The weather in {{ $('Process Weather Data').first().json.city }} is {{ $('Process Weather Data').first().json.condition }} with a temperature of {{ $('Process Weather Data').first().json.temperature }}°C{{ $('Process Weather Data').first().json.aqi ? ' and air quality PM2.5 of ' + $('Process Weather Data').first().json.aqi + ' μg/m³' : '' }}. Give me a brief, friendly comment about this weather.\"}]"}, {"name": "max_tokens", "value": 150}, {"name": "temperature", "value": 0.7}]}, "options": {}}, "id": "bf79a75e-58e9-406b-a10a-eaa2da43dfa4", "name": "Generate AI Commentary", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [480, 620]}, {"parameters": {"jsCode": "const inputData = $input.first().json;\nlet ai_commentary;\n\n// Try to extract AI commentary from response, with fallback\ntry {\n  if (inputData.choices && inputData.choices[0] && inputData.choices[0].message) {\n    ai_commentary = inputData.choices[0].message.content.trim();\n  } else {\n    throw new Error('No AI response available');\n  }\n} catch (error) {\n  // Fallback commentary based on temperature - get from the Process Weather Data node\n  const weatherData = $('Process Weather Data').first().json;\n  const temp = weatherData.temperature;\n  const condition = weatherData.condition;\n  \n  if (temp <= 0) {\n    ai_commentary = \"Bundle up! It's freezing out there. Perfect weather for hot cocoa and staying cozy indoors.\";\n  } else if (temp <= 10) {\n    ai_commentary = \"Quite chilly today! A warm jacket and scarf would be your best friends.\";\n  } else if (temp <= 20) {\n    ai_commentary = \"Pleasant and mild weather. Great for a comfortable walk or outdoor activities!\";\n  } else if (temp <= 30) {\n    ai_commentary = \"Lovely warm weather! Perfect for spending time outdoors and soaking up some sun.\";\n  } else {\n    ai_commentary = \"Quite hot today! Stay hydrated, seek shade, and keep cool. Early morning or evening might be better for outdoor activities.\";\n  }\n}\n\n// Get the original data from Process Weather Data node\nconst originalData = $('Process Weather Data').first().json;\nreturn {\n  json: {\n    ...originalData,\n    ai_commentary: ai_commentary\n  }\n};"}, "id": "35cbe914-d21d-44c0-be02-7c2ab796a066", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 620]}, {"parameters": {"operation": "update", "tableId": "weather_reports", "filterType": "manual", "matchingColumns": [{"column": "email", "value": "={{ $json.email }}"}, {"column": "timestamp", "value": "={{ $json.timestamp }}"}], "updateColumns": [{"column": "ai_commentary", "value": "={{ $json.ai_commentary }}"}]}, "id": "a7a1ee53-0112-426e-befd-25fc73dce370", "name": "Store AI Commentary", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [920, 620], "credentials": {"supabaseApi": {"id": "Ur7gtiGXUbgMtBAi", "name": "Supabase account"}}}, {"parameters": {"sendTo": "={{ $json.email }}", "subject": "🌤 Your Personal Weather Report for {{ $json.city }}", "message": "=<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <style>\n        body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f5f5f5; }\n        .container { background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }\n        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 30px; text-align: center; }\n        .header h1 { margin: 0; font-size: 2.2em; font-weight: 300; }\n        .header p { margin: 10px 0 0 0; opacity: 0.9; font-size: 1.1em; }\n        .content { padding: 40px 30px; }\n        .greeting { font-size: 1.1em; margin-bottom: 25px; }\n        .weather-card { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 30px; border-radius: 12px; margin: 25px 0; text-align: center; border: 1px solid #dee2e6; }\n        .temp { font-size: 3.5em; font-weight: bold; color: #667eea; margin: 0; }\n        .condition { font-size: 1.4em; color: #495057; margin: 15px 0; text-transform: capitalize; }\n        .aqi { padding: 15px 20px; border-radius: 8px; margin: 20px 0; font-weight: 500; }\n        .aqi-good { background: #d1f2eb; color: #0d7a5f; border: 1px solid #7dcea0; }\n        .aqi-moderate { background: #fef9e7; color: #b7950b; border: 1px solid #f4d03f; }\n        .aqi-unhealthy { background: #fadbd8; color: #a93226; border: 1px solid #ec7063; }\n        .ai-comment { background: #e8f4fd; padding: 25px; border-left: 5px solid #3498db; margin: 25px 0; border-radius: 0 8px 8px 0; }\n        .ai-comment-header { font-weight: bold; color: #2980b9; margin-bottom: 10px; font-size: 1.1em; }\n        .footer { text-align: center; margin-top: 40px; padding-top: 25px; border-top: 1px solid #eee; color: #666; }\n        .footer p { margin: 5px 0; }\n        .footer small { color: #999; }\n        .city-name { color: #667eea; font-weight: 600; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>🌤 Weather Report</h1>\n            <p>Personal weather summary for {{ $json.name }}</p>\n        </div>\n        \n        <div class=\"content\">\n            <div class=\"greeting\">Hi {{ $json.name }},</div>\n            \n            <p>Thanks for requesting your personalized weather report! Here's the current weather information for <span class=\"city-name\">{{ $json.city }}</span>:</p>\n            \n            <div class=\"weather-card\">\n                <div class=\"temp\">{{ $json.temperature }}°C</div>\n                <div class=\"condition\">{{ $json.condition }}</div>\n                \n                {{ $json.aqi ? '<div class=\"aqi ' + ($json.aqi <= 50 ? 'aqi-good' : $json.aqi <= 100 ? 'aqi-moderate' : 'aqi-unhealthy') + '\">🌬️ Air Quality (PM2.5): ' + $json.aqi + ' μg/m³ - ' + ($json.aqi <= 50 ? 'Good' : $json.aqi <= 100 ? 'Moderate' : 'Unhealthy') + '</div>' : '' }}\n            </div>\n            \n            <div class=\"ai-comment\">\n                <div class=\"ai-comment-header\">💬 AI Weather Insight</div>\n                {{ $json.ai_commentary }}\n            </div>\n            \n            <p>Stay safe and have a wonderful day!</p>\n            \n            \n            <div class=\"footer\">\n                <p><strong>Automated Weather Report System</strong></p>\n                <p>Built with ❤️ by <strong>Bollineni Narendra Chowdary</strong></p>\n                <small>Powered by WeatherAPI.com • n8n Automation • OpenRouter AI • Gmail</small>\n            </div>\n        </div>\n    </div>\n</body>\n</html>", "options": {}}, "id": "88b62094-7738-42de-98a1-6ce0281337c1", "name": "Send Weather Email", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [1140, 620], "webhookId": "fc20c2e0-7a7b-418d-ba38-7ff4368fad49", "credentials": {"gmailOAuth2": {"id": "t2TQLI3d1qBqVwFx", "name": "Gmail account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Weather report sent successfully!\",\n  \"data\": {\n    \"email\": \"{{ $json.email }}\",\n    \"city\": \"{{ $json.city }}\",\n    \"temperature\": {{ $json.temperature }},\n    \"condition\": \"{{ $json.condition }}\",\n    \"timestamp\": \"{{ $json.timestamp }}\"\n  }\n}", "options": {}}, "id": "424188ec-7ef9-4e51-ac7f-dad4937de43e", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1360, 620]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"message\": \"{{ $json.email_valid ? 'Weather data could not be retrieved for ' + $json.city : 'Invalid email address provided' }}\",\n  \"data\": {\n    \"email\": \"{{ $json.email }}\",\n    \"city\": \"{{ $json.city }}\",\n    \"email_valid\": {{ $json.email_valid }},\n    \"weather_success\": {{ $json.weather_fetch_success }},\n    \"timestamp\": \"{{ $json.timestamp }}\"\n  }\n}", "options": {}}, "id": "98c95937-d4de-4d6f-aa81-1c0041df8689", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [480, 920]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"message\": \"Invalid email address provided. Please enter a valid email address.\",\n  \"data\": {\n    \"email\": \"{{ $json.body.email }}\",\n    \"city\": \"{{ $json.body.city }}\",\n    \"fullName\": \"{{ $json.body.fullName }}\",\n    \"email_valid\": false,\n    \"timestamp\": \"{{ new Date().toISOString() }}\"\n  }\n}", "options": {}}, "id": "d372afdb-7e9e-4fe8-9498-76268229a2d4", "name": "Invalid Email Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [-400, 920]}, {"parameters": {"content": "## 📧 Gmail OAuth2 Configuration\n\n**Setup Steps:**\n1. Enable Gmail API in Google Cloud Console\n2. Create OAuth2 credentials (Client ID + Secret)\n3. Add authorized redirect URI:\n   `http://localhost:5678/rest/oauth2-credential/callback`\n4. Configure Gmail credential in n8n\n5. Complete OAuth2 flow\n\n**Scopes Required:**\n- `https://www.googleapis.com/auth/gmail.send`\n\n**Features:**\n- Beautiful HTML template\n- Responsive design  \n- AQI color coding\n- AI commentary integration\n- OAuth2 security", "height": 560, "width": 440}, "id": "c3dca1cb-1226-4986-9f3a-daaadb20d26b", "name": "📧 Gmail OAuth2 Config", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1000, 860]}, {"parameters": {"content": "## 🗄️ Enhanced Supabase Integration\n\n**Database Schema Update:**\n\n\nRun the sql command in the codebase to create proper things in the supabse\n\n**Two-Stage Process:**\n1. **Initial Insert** (after weather fetch)\n   - Logs all submissions\n   - Includes validation status\n   - Records weather data (if available)\n\n2. **Update with AI Commentary** (after AI generation)\n   - Adds AI insights to existing record\n   - Matches by email + timestamp\n   - Completes the data record\n\n**Benefits:**\n- Complete audit trail\n- Fault tolerance\n- Enhanced analytics", "height": 600, "width": 360}, "id": "eadc06e9-33a8-47c1-8ce9-a756afbcd8fb", "name": "🗄️ Enhanced Supabase", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-80, 960]}, {"parameters": {"content": "## 🔀 Enhanced Workflow Logic\n\n**All submissions → Supabase**\n- Every form submission logged initially\n- Includes validation & weather status\n\n**Email sent only if:**\n- ✅ Valid email format\n- ✅ Weather data retrieved successfully\n\n**AI Commentary Process:**\n- Primary: OpenRouter/Gemini API\n- Fallback: Temperature-based logic\n- Always adds commentary to database\n\n**Error Handling:**\n- ❌ Error response to user\n- 📊 Still logged in database\n- 🔄 Graceful fallbacks throughout", "height": 480, "width": 360}, "id": "242080f7-6a10-4c5f-8487-ff5468fe29af", "name": "🔀 Enhanced Logic Flow", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [340, 1120]}, {"parameters": {"content": "## 🚀 Setup Requirements\n\n**Environment Variables:**\n- `WEATHER_API_KEY`: WeatherAPI.com key\n- `OPENROUTER_API_KEY`: OpenRouter API key\n- Supabase URL & Key in node config\n- Gmail OAuth2 in credential manager\n\n**Database Setup:**\n- Update schema with `ai_commentary` column\n- Ensure proper indexing for performance\n- Configure Row Level Security if needed\n\n**Test Flow:**\n1. Submit valid form data\n2. Verify database logging\n3. Check email delivery\n4. Validate AI commentary storage", "height": 440, "width": 380}, "id": "46e9b331-888f-4518-bcbf-68154517a118", "name": "🚀 Setup Guide", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-360, 140]}], "connections": {"Webhook": {"main": [[{"node": "Email Validation", "type": "main", "index": 0}]]}, "Email Validation": {"main": [[{"node": "Fetch Weather Data", "type": "main", "index": 0}], [{"node": "Invalid Email Response", "type": "main", "index": 0}]]}, "Fetch Weather Data": {"main": [[{"node": "Process Weather Data", "type": "main", "index": 0}]]}, "Process Weather Data": {"main": [[{"node": "Store Initial Data", "type": "main", "index": 0}]]}, "Store Initial Data": {"main": [[{"node": "Check Email Valid & Weather Success", "type": "main", "index": 0}]]}, "Check Email Valid & Weather Success": {"main": [[{"node": "Generate AI Commentary", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Generate AI Commentary": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Store AI Commentary", "type": "main", "index": 0}]]}, "Store AI Commentary": {"main": [[{"node": "Send Weather Email", "type": "main", "index": 0}]]}, "Send Weather Email": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "95dc92cb4cfb9c25a2aaaadc889b98d8831d01195baaba80d8c5ddabbc1cf864"}}